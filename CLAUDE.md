# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a secure Telegram Multi-Account Manager that automatically organizes new group invitations into specified folders across multiple Telegram accounts. The application is built with enterprise-grade security features and production-ready deployment capabilities.

## Core Technologies

- **Python 3.12+** with `uv` package manager
- **Telethon** - Telegram client library
- **Docker & Docker Compose** - Containerized deployment
- **Secure session management** with file permissions (600/700)
- **AES-256-CBC encryption** for backups

## Essential Commands

### Development
```bash
# Install dependencies (uses uv.lock)
uv sync

# Authenticate Telegram accounts (must run first)
python telegram_auth.py

# Run the main application
python telegram_manager.py

# Health check
python -c "import sys; sys.exit(0)"
```

### Docker Operations
```bash
# Build and start containers
docker-compose up -d

# View logs
docker-compose logs -f telegram-manager

# Stop services
docker-compose down

# Rebuild after code changes
docker-compose up -d --build
```

### Production Deployment
```bash
# Automated server setup (Digital Ocean)
sudo ./deploy.sh

# Start production service
sudo systemctl start telegram-manager
sudo systemctl status telegram-manager

# View service logs
journalctl -u telegram-manager -f
```

### Backup Operations
```bash
# Create encrypted backup (run as telegram user)
./backup.sh

# Restore from backup (run as telegram user)
./restore.sh
```

## Architecture Overview

### Main Components

1. **telegram_auth.py** - Handles secure authentication for multiple Telegram accounts
   - Interactive authentication with 2FA support
   - Secure session file management with proper permissions
   - Environment variable validation and cleanup

2. **telegram_manager.py** - Main application that monitors and organizes groups
   - Multi-account event listening
   - Automatic folder organization for new group invitations
   - Resilient error handling with exponential backoff
   - Health monitoring and logging

3. **SecureSessionManager** (shared class) - Manages session file security
   - Enforces 700 permissions on session directories
   - Sets 600 permissions on individual session files
   - Handles Docker-compatible path resolution

### Security Architecture

- **Session Storage**: All Telegram session files stored in `telegram_sessions/` with 700 directory permissions
- **Non-root Execution**: Application runs as dedicated `telegram` user (UID 1000)
- **Container Isolation**: Read-only filesystem with secure tmpfs mounts
- **Encrypted Backups**: AES-256-CBC encryption with PBKDF2 key derivation
- **Network Security**: Isolated Docker networks with firewall rules

## Environment Configuration

### Required Environment Variables
```bash
# Primary account
TELEGRAM_ACCOUNT_1_API_ID="your_api_id"
TELEGRAM_ACCOUNT_1_API_HASH="your_api_hash"  
TELEGRAM_ACCOUNT_1_PHONE="+**********"

# Target folder for organization
TARGET_TELEGRAM_FOLDER_NAME="New Groups"

# Optional: Custom session path (Docker compatibility)
TELEGRAM_SESSIONS_PATH="/app/telegram_sessions"
```

### Multi-Account Support
- Supports up to 5 accounts by incrementing numbers (TELEGRAM_ACCOUNT_2_*, etc.)
- Each account requires API_ID, API_HASH, and PHONE
- Missing accounts are automatically skipped

## Development Workflow

### Authentication Flow
1. **Always run `python telegram_auth.py` first** - This creates and validates session files
2. Sessions are persistent and secure - re-authentication only needed if sessions expire
3. The main manager will skip unauthenticated accounts with warnings

### Code Organization
- **Shared SecureSessionManager**: Both auth and manager use identical session management
- **Event-driven architecture**: Uses Telethon's event system for real-time group monitoring
- **Retry logic**: Exponential backoff for rate limiting and network failures
- **Comprehensive logging**: All operations logged with timestamps and context

### Security Considerations
- **Never commit session files** - `telegram_sessions/` contains sensitive authentication data
- **File permissions are critical** - The application enforces 600/700 permissions
- **Environment variables only** - No hardcoded credentials allowed
- **Backup encryption keys** - Generated automatically and secured with 600 permissions

## Production Deployment Notes

### Digital Ocean Deployment
- `deploy.sh` provides automated Ubuntu 22.04 LTS setup
- Creates dedicated system user and secure directories
- Configures UFW firewall and fail2ban for SSH protection
- Sets up systemd service for auto-start and monitoring

### Docker Security Features
- Non-root container execution (user: telegram, UID 1000)
- Read-only root filesystem with secure tmpfs mounts
- Resource limits and health checks configured
- Isolated bridge network with restricted IP ranges

### Backup Strategy
- Automated daily backups with 30-day retention
- AES-256-CBC encryption using generated keys
- Backup verification and cleanup included
- Restore capability with current session backup

## Common Issues and Solutions

### Authentication Problems
- Run `python telegram_auth.py` to re-authenticate accounts
- Check API credentials in environment variables
- Verify phone number format includes country code

### Permission Issues
- Ensure session files have 600 permissions
- Session directory must have 700 permissions
- Application must run as `telegram` user in production

### Docker Issues
- Rebuild containers after code changes: `docker-compose up -d --build`
- Check logs: `docker-compose logs telegram-manager`
- Verify volume mounts for session persistence

## File Structure Notes

- `archived/` - Contains historical documentation and dashboard components
- `telegram_sessions/` - Secure session storage (never commit these files)
- `.env.production` - Production environment template
- `pyproject.toml` & `uv.lock` - Python dependency management
- Shell scripts are production-ready with error handling and logging