#!/bin/bash

# Secure Telegram Manager Deployment Script for Digital Ocean
# This script sets up the application with proper security measures

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
APP_NAME="telegram-manager"
APP_DIR="/opt/telegram-app"
SESSIONS_DIR="$APP_DIR/sessions"
LOGS_DIR="$APP_DIR/logs"
USER="telegram"
GROUP="telegram"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   error "This script must be run as root (use sudo)"
fi

log "Starting secure deployment of Telegram Manager..."

# Update system packages
log "Updating system packages..."
apt-get update && apt-get upgrade -y

# Install required packages
log "Installing required packages..."
apt-get install -y \
    docker.io \
    docker-compose \
    ufw \
    fail2ban \
    logrotate \
    curl \
    wget

# Start and enable Docker
log "Starting Docker service..."
systemctl start docker
systemctl enable docker

# Create application user and group
log "Creating application user and directories..."
if ! getent group "$GROUP" > /dev/null 2>&1; then
    groupadd "$GROUP"
fi

if ! getent passwd "$USER" > /dev/null 2>&1; then
    useradd -r -g "$GROUP" -d "$APP_DIR" -s /bin/bash "$USER"
fi

# Create secure directories
mkdir -p "$APP_DIR" "$SESSIONS_DIR" "$LOGS_DIR"

# Set proper ownership and permissions
chown -R "$USER:$GROUP" "$APP_DIR"
chmod 755 "$APP_DIR"
chmod 700 "$SESSIONS_DIR"  # Secure sessions directory
chmod 755 "$LOGS_DIR"

log "Directory structure created with secure permissions"

# Configure firewall
log "Configuring firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 22/tcp
# Add any other ports your application needs
# ufw allow 8080/tcp  # Example for web interface

ufw --force enable

log "Firewall configured successfully"

# Configure fail2ban for SSH protection
log "Configuring fail2ban..."
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
EOF

systemctl enable fail2ban
systemctl restart fail2ban

# Set up log rotation
log "Setting up log rotation..."
cat > /etc/logrotate.d/telegram-manager << EOF
$LOGS_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $GROUP
    postrotate
        docker-compose -f $APP_DIR/docker-compose.yml restart telegram-manager || true
    endscript
}
EOF

# Create systemd service for auto-start
log "Creating systemd service..."
cat > /etc/systemd/system/telegram-manager.service << EOF
[Unit]
Description=Telegram Manager Docker Compose
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/docker-compose up -d
ExecStop=/usr/bin/docker-compose down
TimeoutStartSec=0
User=$USER
Group=$GROUP

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable telegram-manager.service

log "Deployment infrastructure setup complete!"
log "Next steps:"
log "1. Copy your application files to $APP_DIR"
log "2. Create and configure $APP_DIR/.env.production with your credentials"
log "3. Run: sudo systemctl start telegram-manager"
log ""
log "Security features enabled:"
log "- Firewall (UFW) configured"
log "- Fail2ban for SSH protection"
log "- Non-root user for application"
log "- Secure file permissions (700 for sessions)"
log "- Log rotation configured"
log "- Systemd service for auto-start"
