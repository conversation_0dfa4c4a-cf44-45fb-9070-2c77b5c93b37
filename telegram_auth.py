import asyncio
import logging
import os
import stat
from pathlib import Path
from typing import List, Dict, Any

from telethon import TelegramClient
from telethon.errors import (
    SessionPasswordNeededError,
    PhoneCodeInvalidError,
    ApiIdInvalidError,
    PhoneNumberInvalidError,
)
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class SecureSessionManager:
    """Simple secure session management with file permissions."""

    def __init__(self):
        # Create secure sessions directory - use environment variable for Docker compatibility
        sessions_path = os.getenv("TELEGRAM_SESSIONS_PATH", "telegram_sessions")
        self.sessions_dir = Path(sessions_path)
        self.sessions_dir.mkdir(mode=0o700, exist_ok=True)  # Only owner can read/write/execute

        # Set secure permissions on the directory
        try:
            os.chmod(self.sessions_dir, stat.S_IRWXU)  # 700 permissions
            logger.info(f"Secured sessions directory: {self.sessions_dir}")
        except Exception as e:
            logger.warning(f"Could not set directory permissions: {e}")

        # Log session directory for debugging
        logger.info(f"Using sessions directory: {self.sessions_dir.absolute()}")

    def get_session_path(self, session_name: str) -> Path:
        """Get secure session file path."""
        return self.sessions_dir / f"{session_name}.session"

    def secure_session_file(self, session_path: Path):
        """Set secure permissions on session file."""
        try:
            if session_path.exists():
                # Set to read/write for owner only (600)
                os.chmod(session_path, stat.S_IRUSR | stat.S_IWUSR)
                logger.debug(f"Secured session file: {session_path}")
        except Exception as e:
            logger.warning(f"Could not set file permissions for {session_path}: {e}")

    def session_exists(self, session_name: str) -> bool:
        """Check if session file exists."""
        return self.get_session_path(session_name).exists()

    def delete_session(self, session_name: str) -> bool:
        """Securely delete session file."""
        try:
            session_path = self.get_session_path(session_name)
            if session_path.exists():
                session_path.unlink()
                logger.info(f"Deleted session: {session_name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting session {session_name}: {e}")
            return False


class TelegramAuthenticator:
    """Handles authentication for multiple Telegram accounts with secure storage."""

    def __init__(self):
        self.accounts: List[Dict[str, Any]] = []
        self.session_manager = SecureSessionManager()

    async def load_environment(self) -> bool:
        """Load environment variables."""
        try:
            script_dir = Path(__file__).resolve().parent
            env_path = script_dir / ".env"

            if env_path.exists():
                logger.info(f"Loading .env file from: {env_path}")
                load_dotenv(dotenv_path=env_path, override=True)
            else:
                logger.warning(f".env file not found at: {env_path}")
                load_dotenv()

            return True
        except Exception as e:
            logger.error(f"Error loading environment: {e}", exc_info=True)
            return False

    async def load_accounts_from_env(self) -> bool:
        """Load account configurations from environment variables."""
        try:
            self.accounts = []
            i = 1

            while True:
                api_id = os.getenv(f"TELEGRAM_ACCOUNT_{i}_API_ID")
                api_hash = os.getenv(f"TELEGRAM_ACCOUNT_{i}_API_HASH")
                phone = os.getenv(f"TELEGRAM_ACCOUNT_{i}_PHONE")

                if not (api_id and api_hash):
                    break

                # Clean and validate API ID
                try:
                    api_id_clean = str(api_id).strip('"\'')
                    api_id_int = int(api_id_clean)
                except (ValueError, TypeError):
                    logger.error(f"Invalid API ID for account {i}: {api_id}")
                    i += 1
                    continue

                api_hash_clean = str(api_hash).strip('"\'')
                phone_clean = str(phone).strip('"\'') if phone else None

                if not phone_clean:
                    logger.warning(f"Account {i} has no phone number - skipping")
                    i += 1
                    continue

                account_config = {
                    "api_id": api_id_int,
                    "api_hash": api_hash_clean,
                    "phone": phone_clean,
                    "session_name": f"account_{i}",
                    "session_path": str(self.session_manager.get_session_path(f"account_{i}")),
                    "account_number": i,
                }

                self.accounts.append(account_config)
                logger.info(f"Loaded configuration for account {i}")
                i += 1

            if not self.accounts:
                logger.error("No valid account configurations found")
                return False

            logger.info(f"Loaded {len(self.accounts)} account configurations")
            return True

        except Exception as e:
            logger.error(f"Error loading accounts: {e}", exc_info=True)
            return False

    async def check_authentication_status(self):
        """Check which accounts are authenticated and which need setup."""
        authenticated = []
        needs_auth = []

        for account in self.accounts:
            session_path = Path(account['session_path'])
            if session_path.exists():
                # Try to connect and check if authenticated
                client = TelegramClient(
                    account["session_path"],
                    account["api_id"],
                    account["api_hash"]
                )

                try:
                    await client.connect()
                    if await client.is_user_authorized():
                        me = await client.get_me()
                        authenticated.append({
                            **account,
                            "user_name": me.first_name,
                            "user_id": me.id
                        })
                        logger.info(
                            f"✓ Account {account['account_number']}: {me.first_name} (authenticated)"
                        )
                        # Secure the session file
                        self.session_manager.secure_session_file(session_path)
                    else:
                        needs_auth.append(account)
                        logger.warning(
                            f"✗ Account {account['account_number']}: session exists but not authorized"
                        )
                except Exception as e:
                    needs_auth.append(account)
                    logger.warning(
                        f"✗ Account {account['account_number']}: error checking auth - {e}"
                    )
                finally:
                    if client.is_connected():
                        await client.disconnect()
            else:
                needs_auth.append(account)
                logger.warning(
                    f"✗ Account {account['account_number']}: no session file"
                )

        return authenticated, needs_auth

    async def authenticate_account(self, account: Dict[str, Any]) -> bool:
        """Authenticate a single account interactively."""
        account_number = account["account_number"]
        phone = account["phone"]
        session_path = account["session_path"]

        logger.info(f"\n{'='*50}")
        logger.info(f"Authenticating Account {account_number}")
        logger.info(f"Phone: {phone}")
        logger.info(f"{'='*50}")

        client = TelegramClient(
            session_path,
            account["api_id"],
            account["api_hash"]
        )

        try:
            await client.connect()

            if await client.is_user_authorized():
                me = await client.get_me()
                logger.info(f"Account {account_number} already authenticated as {me.first_name}")
                # Secure the session file
                self.session_manager.secure_session_file(Path(session_path))
                return True

            # Send code request
            logger.info(f"Sending authentication code to {phone}...")
            await client.send_code_request(phone)

            # Get code from user
            while True:
                try:
                    code = input(f"Enter the code sent to {phone}: ").strip()
                    if code:
                        break
                    print("Please enter a valid code.")
                except KeyboardInterrupt:
                    logger.info("Authentication cancelled by user")
                    return False

            # Sign in with code
            try:
                await client.sign_in(phone, code)
            except SessionPasswordNeededError:
                # Two-factor authentication
                while True:
                    try:
                        password = input("Two-factor authentication enabled. Enter your password: ").strip()
                        if password:
                            await client.sign_in(password=password)
                            break
                        print("Please enter your password.")
                    except KeyboardInterrupt:
                        logger.info("Authentication cancelled by user")
                        return False
                    except Exception as e:
                        logger.error(f"Invalid password: {e}")
                        continue

            # Verify authentication and secure session
            me = await client.get_me()
            logger.info(f"✓ Successfully authenticated account {account_number} as {me.first_name} (ID: {me.id})")

            # Secure the session file
            self.session_manager.secure_session_file(Path(session_path))
            return True

        except PhoneNumberInvalidError:
            logger.error(f"Invalid phone number: {phone}")
            return False
        except PhoneCodeInvalidError:
            logger.error("Invalid authentication code")
            return False
        except ApiIdInvalidError:
            logger.error(f"Invalid API ID or API Hash for account {account_number}")
            return False
        except Exception as e:
            logger.error(f"Error authenticating account {account_number}: {e}", exc_info=True)
            return False
        finally:
            if client.is_connected():
                await client.disconnect()

    async def authenticate_all_needed(self, needs_auth: List[Dict[str, Any]]) -> bool:
        """Authenticate all accounts that need authentication."""
        if not needs_auth:
            logger.info("All accounts are already authenticated!")
            return True

        logger.info(f"\nFound {len(needs_auth)} accounts that need authentication:")
        for account in needs_auth:
            print(f"  - Account {account['account_number']}: {account['phone']}")

        # Ask for confirmation
        try:
            confirm = input(f"\nAuthenticate these {len(needs_auth)} accounts? (y/N): ").strip().lower()
            if confirm not in ['y', 'yes']:
                logger.info("Authentication cancelled")
                return False
        except KeyboardInterrupt:
            logger.info("Authentication cancelled")
            return False

        # Authenticate each account
        success_count = 0
        for account in needs_auth:
            try:
                if await self.authenticate_account(account):
                    success_count += 1
                else:
                    logger.warning(f"Failed to authenticate account {account['account_number']}")
            except KeyboardInterrupt:
                logger.info("Authentication process interrupted")
                break

        logger.info(f"\nAuthentication complete: {success_count}/{len(needs_auth)} accounts authenticated")
        return success_count > 0

    async def run(self):
        """Main authentication workflow."""
        try:
            if not await self.load_environment():
                return False

            if not await self.load_accounts_from_env():
                return False

            logger.info("Checking authentication status...")
            authenticated, needs_auth = await self.check_authentication_status()

            logger.info(f"\nAuthentication Summary:")
            logger.info(f"✓ Authenticated accounts: {len(authenticated)}")
            logger.info(f"✗ Accounts needing authentication: {len(needs_auth)}")

            if authenticated:
                logger.info("\nAuthenticated accounts:")
                for acc in authenticated:
                    logger.info(f"  - Account {acc['account_number']}: {acc['user_name']} ({acc['phone']})")

            if needs_auth:
                return await self.authenticate_all_needed(needs_auth)
            else:
                logger.info("\nAll accounts are ready!")
                return True

        except Exception as e:
            logger.error(f"Error in authentication process: {e}", exc_info=True)
            return False


async def main():
    """Main function."""
    print("🔐 Secure Telegram Account Authenticator")
    print("=" * 40)

    authenticator = TelegramAuthenticator()
    success = await authenticator.run()

    if success:
        logger.info("\n✓ Authentication process completed successfully!")
        logger.info("Sessions are stored securely in 'telegram_sessions/' directory")
        logger.info("You can now run the main telegram_manager.py script.")
    else:
        logger.error("\n✗ Authentication process failed or was cancelled.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\nAuthentication process interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)