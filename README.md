Before we begin, ensure you have:
  - SSH access to your Digital Ocean server (IPV4)
  - Your Telegram API credentials (API ID, API Hash, Phone numbers)
  - Your local project files ready to upload

docker-compose up -d telegram-manager

  Step 1: Initial Server Connection

  Connect to your server:
  # Connect to your Digital Ocean server
  ssh root@IPV4
  # or if you have a non-root user:
  ssh your_username@IPV4

  Step 2: Server Preparation

  Run these commands on your server (IPV4):

  # Update system packages
  apt update && apt upgrade -y

  # Install essential tools
  apt install -y git curl wget unzip software-properties-common nano htop

  # Create application directory
  mkdir -p /opt/telegram-app
  cd /opt/telegram-app

  Step 3: Upload Your Application Files

  From your local machine, upload the project files:

  # Option 1: Using SCP (from your local machine)
  scp -r /Users/<USER>/Downloads/Coding/IBC/os-python/* root@IPV4:/opt/telegram-app/

  # Option 2: Using rsync (recommended)
  rsync -avz --progress /Users/<USER>/Downloads/Coding/IBC/os-python/ root@IPV4:/opt/telegram-app/

  # Option 3: Git clone (if you have a repository)
  # ssh root@IPV4
  # cd /opt/telegram-app
  # git clone https://github.com/yourusername/your-repo.git .

  Step 4: Run the Automated Deployment

  Back on your server (IPV4):

  # Make deployment script executable
  chmod +x /opt/telegram-app/deploy.sh

  # Run the deployment script
  sudo /opt/telegram-app/deploy.sh

  This will automatically:
  - Install Docker and Docker Compose
  - Create secure user accounts
  - Set up firewall rules
  - Configure fail2ban for SSH protection
  - Create systemd services
  - Set up log rotation

  Step 5: Configure Environment Variables

  Create your production environment file:

  # Create environment file
  nano /opt/telegram-app/.env.production

  Add your Telegram credentials:
  # Your Telegram API Configuration
  TELEGRAM_ACCOUNT_1_API_ID=your_api_id_here
  TELEGRAM_ACCOUNT_1_API_HASH=your_api_hash_here
  TELEGRAM_ACCOUNT_1_PHONE=+**********

  # Add more accounts if needed
  TELEGRAM_ACCOUNT_2_API_ID=second_api_id
  TELEGRAM_ACCOUNT_2_API_HASH=second_api_hash
  TELEGRAM_ACCOUNT_2_PHONE=+**********

  # Target folder for moving groups
  TARGET_TELEGRAM_FOLDER_NAME=YourDesiredFolderName

  # Session storage path
  TELEGRAM_SESSIONS_PATH=/app/telegram_sessions

  Step 6: Authenticate Telegram Accounts

  # Switch to the application directory
  cd /opt/telegram-app

  # Build Docker image
  docker-compose build

  # Run authentication (interactive)
  docker-compose run --rm telegram-manager python telegram_auth.py

  Follow the prompts to:
  1. Enter your phone numbers
  2. Provide verification codes sent to your phone
  3. Enter 2FA passwords if enabled

  Step 7: Start the Application

  # Start the service
  systemctl start telegram-manager

  # Check status
  systemctl status telegram-manager

  # Enable auto-start on boot
  systemctl enable telegram-manager

  # View logs
  docker-compose logs -f telegram-manager

  Step 8: Verify Installation

  Check that everything is working:

  # Check container status
  docker-compose ps

  # View recent logs
  docker-compose logs --tail=50 telegram-manager

  # Check system resources
  htop

  # Check disk usage
  df -h

  Security Configuration

  Your deployment script has already configured:

  Firewall Rules

  # Check current firewall status
  ufw status

  # The deployment script sets up:
  # - SSH access (port 22)
  # - Deny all other incoming traffic
  # - Allow all outgoing traffic

  Fail2ban Protection

  # Check fail2ban status
  fail2ban-client status

  # View SSH protection
  fail2ban-client status sshd

  Monitoring and Maintenance

  View Live Logs

  # Application logs
  docker-compose -f /opt/telegram-app/docker-compose.yml logs -f

  # System logs
  journalctl -u telegram-manager -f

  # Container resource usage
  docker stats

  Backup Setup

  # Set up automated daily backups
  sudo -u telegram crontab -e

  # Add this line for daily backups at 2 AM
  0 2 * * * /opt/telegram-app/backup.sh

  Manual Backup

  # Run backup manually
  sudo -u telegram /opt/telegram-app/backup.sh

  Troubleshooting

  Common Issues:

  1. Container won't start:
  # Check logs for errors
  docker-compose logs telegram-manager

  # Check if port is already in use
  netstat -tlnp

  # Restart the service
  systemctl restart telegram-manager

  2. Authentication errors:
  # Remove sessions and re-authenticate
  rm -rf /opt/telegram-app/telegram_sessions/*
  docker-compose run --rm telegram-manager python telegram_auth.py

  # Test authentication
  docker-compose run --rm telegram-manager python telegram_manager.py

  3. Permission issues:
  # Fix ownership
  chown -R telegram:telegram /opt/telegram-app
  chmod 700 /opt/telegram-app/telegram_sessions

  4. Out of disk space:
  # Check disk usage
  df -h

  # Clean up Docker images
  docker system prune -a

  # Check log sizes
  du -sh /opt/telegram-app/logs/*

  Useful Commands for Your Server (**********)

  # Quick status check
  systemctl status telegram-manager && docker-compose ps

  # Restart application
  systemctl restart telegram-manager

  # Update application (after uploading new files)
  cd /opt/telegram-app
  docker-compose down
  docker-compose build
  systemctl start telegram-manager

  # Check resource usage
  htop
  df -h
  docker stats

  # Emergency stop
  systemctl stop telegram-manager
  docker-compose down

  Remote Access & Management

  SSH Connection Examples:

  # Direct connection
  ssh root@**********

  # With custom port (if changed)
  ssh -p 2222 root@**********

  # Copy files to server
  scp file.txt root@**********:/opt/telegram-app/

  # Download logs from server
  scp root@**********:/opt/telegram-app/logs/*.log ./local-logs/

  Performance Optimization

  For better performance on your server:

  # Optimize Docker settings
  echo '{"log-driver": "json-file", "log-opts": {"max-size": "10m", "max-file": "3"}}' > /etc/docker/daemon.json
  systemctl restart docker

  # Set up swap if needed (for low memory servers)
  fallocate -l 2G /swapfile
  chmod 600 /swapfile
  mkswap /swapfile
  swapon /swapfile
  echo '/swapfile none swap sw 0 0' >> /etc/fstab

  This deployment guide is specifically tailored for your Digital Ocean server at **********. Your application includes
  excellent security features and Docker configuration, so following these steps should get you up and running quickly and
   securely.

  Would you like me to help you with any specific part of this deployment process?