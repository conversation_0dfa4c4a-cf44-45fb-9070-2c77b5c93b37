{"permissions": {"allow": ["Bash(ls:*)", "Bash(uv sync:*)", "Bash(uv tree:*)", "Bash(grep:*)", "<PERSON><PERSON>(uv:*)", "<PERSON><PERSON>(python:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(docker build:*)", "Bash(docker-compose config:*)", "Bash(timeout 30 docker-compose up --build --abort-on-container-exit 2 >& 1)", "<PERSON><PERSON>(docker run:*)", "Bash(git add:*)", "Bash(find:*)", "Bash(git push:*)", "Bash(git checkout:*)", "Bash(git pull:*)", "Bash(git merge:*)"], "deny": []}}