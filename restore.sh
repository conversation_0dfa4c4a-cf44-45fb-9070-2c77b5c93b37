#!/bin/bash

# Secure Restore Script for Telegram Manager Sessions
# This script restores encrypted session backups

set -euo pipefail

# Configuration
APP_DIR="/opt/telegram-app"
SESSIONS_DIR="$APP_DIR/sessions"
BACKUP_DIR="/opt/telegram-app/backups"
ENCRYPTION_KEY_FILE="/opt/telegram-app/.backup_key"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as telegram user
if [[ $(whoami) != "telegram" ]]; then
    error "This script must be run as the telegram user"
fi

# Check if encryption key exists
if [[ ! -f "$ENCRYPTION_KEY_FILE" ]]; then
    error "Encryption key not found: $ENCRYPTION_KEY_FILE"
fi

# List available backups
log "Available backups:"
if ! ls -lh "$BACKUP_DIR"/sessions_backup_*.tar.gz.enc 2>/dev/null; then
    error "No backup files found in $BACKUP_DIR"
fi

echo ""
read -p "Enter the backup filename to restore (or 'latest' for most recent): " BACKUP_CHOICE

# Handle 'latest' option
if [[ "$BACKUP_CHOICE" == "latest" ]]; then
    BACKUP_FILE=$(ls -t "$BACKUP_DIR"/sessions_backup_*.tar.gz.enc 2>/dev/null | head -n1)
    if [[ -z "$BACKUP_FILE" ]]; then
        error "No backup files found"
    fi
    log "Using latest backup: $(basename $BACKUP_FILE)"
else
    # Check if user provided full path or just filename
    if [[ "$BACKUP_CHOICE" == *"/"* ]]; then
        BACKUP_FILE="$BACKUP_CHOICE"
    else
        BACKUP_FILE="$BACKUP_DIR/$BACKUP_CHOICE"
    fi
fi

# Verify backup file exists
if [[ ! -f "$BACKUP_FILE" ]]; then
    error "Backup file not found: $BACKUP_FILE"
fi

# Confirm restore operation
echo ""
warn "This will REPLACE all current session files!"
warn "Current sessions directory: $SESSIONS_DIR"
warn "Backup file: $BACKUP_FILE"
echo ""
read -p "Are you sure you want to continue? (yes/no): " CONFIRM

if [[ "$CONFIRM" != "yes" ]]; then
    log "Restore operation cancelled"
    exit 0
fi

# Stop the telegram manager service if running
log "Stopping telegram manager service..."
sudo systemctl stop telegram-manager || warn "Could not stop telegram-manager service"

# Backup current sessions (if any)
if [[ -d "$SESSIONS_DIR" ]] && [[ "$(ls -A $SESSIONS_DIR 2>/dev/null)" ]]; then
    CURRENT_BACKUP="$BACKUP_DIR/current_sessions_$(date +%Y%m%d_%H%M%S).tar.gz"
    log "Backing up current sessions to: $CURRENT_BACKUP"
    tar -czf "$CURRENT_BACKUP" -C "$SESSIONS_DIR" . || warn "Could not backup current sessions"
fi

# Create sessions directory if it doesn't exist
mkdir -p "$SESSIONS_DIR"
chmod 700 "$SESSIONS_DIR"

# Clear existing sessions
log "Clearing existing session files..."
rm -rf "$SESSIONS_DIR"/*

# Restore from backup
log "Restoring sessions from backup..."
if openssl enc -aes-256-cbc -d -pbkdf2 -iter 100000 \
    -pass file:"$ENCRYPTION_KEY_FILE" -in "$BACKUP_FILE" | \
    tar -xzf - -C "$SESSIONS_DIR"; then
    
    log "Sessions restored successfully"
    
    # Set proper permissions
    chmod 700 "$SESSIONS_DIR"
    chmod 600 "$SESSIONS_DIR"/*.session 2>/dev/null || true
    
    # List restored files
    log "Restored session files:"
    ls -la "$SESSIONS_DIR"
    
else
    error "Failed to restore sessions from backup"
fi

# Start the telegram manager service
log "Starting telegram manager service..."
sudo systemctl start telegram-manager || warn "Could not start telegram-manager service"

log "Restore operation completed successfully"
