#!/bin/bash

# Secure Backup Script for Telegram Manager Sessions
# This script creates encrypted backups of session files

set -euo pipefail

# Configuration
APP_DIR="/opt/telegram-app"
SESSIONS_DIR="$APP_DIR/sessions"
BACKUP_DIR="/opt/telegram-app/backups"
BACKUP_RETENTION_DAYS=30
ENCRYPTION_KEY_FILE="/opt/telegram-app/.backup_key"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check if running as telegram user
if [[ $(whoami) != "telegram" ]]; then
    error "This script must be run as the telegram user"
fi

# Create backup directory
mkdir -p "$BACKUP_DIR"
chmod 700 "$BACKUP_DIR"

# Generate encryption key if it doesn't exist
if [[ ! -f "$ENCRYPTION_KEY_FILE" ]]; then
    log "Generating encryption key..."
    openssl rand -base64 32 > "$ENCRYPTION_KEY_FILE"
    chmod 600 "$ENCRYPTION_KEY_FILE"
    log "Encryption key generated and secured"
fi

# Create backup filename with timestamp
BACKUP_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/sessions_backup_$BACKUP_TIMESTAMP.tar.gz.enc"

log "Starting backup of session files..."

# Check if sessions directory exists and has files
if [[ ! -d "$SESSIONS_DIR" ]]; then
    error "Sessions directory not found: $SESSIONS_DIR"
fi

if [[ ! "$(ls -A $SESSIONS_DIR 2>/dev/null)" ]]; then
    warn "No session files found in $SESSIONS_DIR"
    exit 0
fi

# Create encrypted backup
log "Creating encrypted backup: $BACKUP_FILE"
tar -czf - -C "$SESSIONS_DIR" . | \
    openssl enc -aes-256-cbc -salt -pbkdf2 -iter 100000 \
    -pass file:"$ENCRYPTION_KEY_FILE" > "$BACKUP_FILE"

# Verify backup was created
if [[ -f "$BACKUP_FILE" ]]; then
    BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    log "Backup created successfully: $BACKUP_FILE ($BACKUP_SIZE)"
else
    error "Failed to create backup file"
fi

# Clean up old backups
log "Cleaning up backups older than $BACKUP_RETENTION_DAYS days..."
find "$BACKUP_DIR" -name "sessions_backup_*.tar.gz.enc" -mtime +$BACKUP_RETENTION_DAYS -delete

# List current backups
log "Current backups:"
ls -lh "$BACKUP_DIR"/sessions_backup_*.tar.gz.enc 2>/dev/null || log "No backups found"

log "Backup process completed successfully"
