version: '3.8'

services:
  telegram-manager:
    build: .
    container_name: telegram-manager
    restart: unless-stopped
    
    # Security settings  
    read_only: true    # Read-only filesystem for security
    
    # Temporary filesystems for writable areas
    tmpfs:
      - /tmp:noexec,nosuid,nodev,size=100m
      - /var/tmp:noexec,nosuid,nodev,size=50m
    
    # Secure volume mounts
    volumes:
      # Persistent session storage with secure permissions
      - telegram_sessions:/app/telegram_sessions:rw
      # Logs volume
      - telegram_logs:/app/logs:rw
      # Read-only application code (optional, for development)
      # - .:/app:ro
    
    # Environment variables (use .env file or Docker secrets in production)
    env_file:
      - .env.production
    
    # Security options
    security_opt:
      - no-new-privileges:true
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Network security
    networks:
      - telegram-network
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a monitoring service
  # watchtower:
  #   image: containrrr/watchtower
  #   container_name: watchtower
  #   restart: unless-stopped
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock
  #   command: --interval 3600 --cleanup telegram-manager

# Secure volumes with proper permissions
volumes:
  telegram_sessions:
    driver: local
    # Removed host path binding for better container isolation
  telegram_logs:
    driver: local
    # Removed host path binding for better container isolation

# Isolated network
networks:
  telegram-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.enable_icc: "false"  # Disable inter-container communication
    ipam:
      config:
        - subnet: **********/16
          ip_range: **********/24  # Restrict IP range
